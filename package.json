{"name": "tv2z-todo", "version": "1.0.0", "description": "", "main": "server.js", "scripts": {"start": "node server", "server": "nodemon server", "client": "npm start --prefix client", "dev": "concurrently \"npm run server\" \"npm run client\"", "heroku-postbuild": "NPM_CONFIG_PRODUCTION=false npm install --prefix client && npm run build --prefix client"}, "keywords": [], "author": "salih18", "license": "ISC", "dependencies": {"bcryptjs": "^2.4.3", "config": "^3.3.1", "express": "^4.17.1", "express-validator": "^6.6.1", "jsonwebtoken": "^8.5.1", "mongoose": "^5.10.3"}, "devDependencies": {"concurrently": "^5.3.0", "nodemon": "^2.0.4"}}