{"name": "redux-template", "version": "0.1.0", "private": true, "dependencies": {"@fortawesome/fontawesome-svg-core": "^1.2.30", "@fortawesome/free-solid-svg-icons": "^5.14.0", "@fortawesome/react-fontawesome": "^0.1.11", "@testing-library/jest-dom": "^4.2.4", "@testing-library/react": "^9.3.2", "@testing-library/user-event": "^7.1.2", "axios": "^0.19.2", "bootstrap": "^4.5.2", "react": "^16.13.1", "react-bootstrap": "^1.3.0", "react-dom": "^16.13.1", "react-moment": "^0.9.7", "react-redux": "^7.2.0", "react-router-dom": "^5.1.2", "react-scripts": "3.4.1", "react-toastify": "^6.0.8", "redux": "^4.0.5", "redux-devtools-extension": "^2.13.8", "redux-thunk": "^2.3.0"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject"}, "eslintConfig": {"extends": "react-app"}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "proxy": "http://localhost:5000", "devDependencies": {}}