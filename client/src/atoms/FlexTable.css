html {
  width: 100vw;
}
@import url('https://fonts.googleapis.com/css2?family=Lato:wght@300;400;700&display=swap');
.wrapper {
  width: 100%;
  max-width: 1000px;
  margin: 1em auto;
  padding: 1em;
  margin-top: 2rem;
}

/*======================TODO TABLE STYLES =============================== */
/* Table column sizing
  ================================== */
#todo-list-flex-table .tags-cell {
  width: 10%;
}

#todo-list-flex-table .text-cell {
  width: 50%;
}

#todo-list-flex-table .date-cell {
  width: 25%;
}

#todo-list-flex-table .icon-cell {
  width: 15%;
}

/* Apply styles Main Table
    ================================== */
#todo-list-flex-table .flexTable {
  display: -webkit-box;
  display: flex;
  flex-wrap: wrap;
  margin: 0 0 3em 0;
  padding: 0;
  /* box-shadow: 0 0 40px rgba(0, 0, 0, 0.2); */
}

#todo-list-flex-table.flexTable .flexTable-row {
  width: 100%;
  display: -webkit-box;
  display: flex;

  overflow: hidden;
  margin-bottom: 0.5rem;
  border-radius: 0.4rem;
  background-color: #f7f7f7;
  transition: background 0.25s cubic-bezier(0.25, 0.46, 0.45, 0.94);

  font-family: 'Lato', sans-serif;
  font-weight: 300;
}

#todo-list-flex-table.flexTable .flexTable-row.flexTable-row--head {
  background-color: white;
  border: none;
  transition: none;
}
#todo-list-flex-table.flexTable .flexTable-row.flexTable-row--head:hover {
  background-color: white;
  border: none;
  box-shadow: none;
}

#todo-list-flex-table.flexTable .flexTable-row:hover {
  background-color: #ebebeb;
  font-weight: 400;

  box-shadow: 0 14px 28px rgba(0, 0, 0, 0.25), 0 10px 10px rgba(0, 0, 0, 0.22);
}


#todo-list-flex-table.flexTable .flexTable-row-completed {
  border-left: 5px solid #28a745;
}

#todo-list-flex-table.flexTable .flexTable-row .flexTable-cell {
  box-sizing: border-box;
  -webkit-box-flex: 1;
  flex-grow: 1;
  padding: 0.8em 1.2em;
  overflow: hidden;
  list-style: none;
}
#todo-list-flex-table.flexTable .flexTable-row .flexTable-cell.column-heading {
  /* background-color: #43bac0; */
  background-color: #fff;
  color: #000;
  font-size: 18px;
  line-height: 1.4;

  font-family: 'Lato', sans-serif;
  font-weight: 700;
  padding: 1em;
}

#todo-list-flex-table.flexTable .flexTable-row .flexTable-cell .flexTable-cell--heading {
  display: none;
}

#todo-list-flex-table .icon-content > svg {
  margin-left: 10px;

  color: #333a40;
  transition: all 0.25s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

#todo-list-flex-table .icon-content > svg:hover {
  color: #000;
  box-shadow: 0 14px 28px rgba(0, 0, 0, 0.25), 0 10px 10px rgba(0, 0, 0, 0.22);
}

#todo-list-flex-table.flexTable .flexTable-row-active {
  border: 2px solid #000;
  box-shadow: 0 14px 28px rgba(0, 0, 0, 0.25), 0 10px 10px rgba(0, 0, 0, 0.22);
  background-color: #ebebeb;
  animation: active-row 0.5s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

#todo-list-flex-table.flexTable .flexTable-row-active.flexTable-row-deleted {
  border-left: 5px solid red;
}

#todo-list-flex-table.flexTable .flexTable-row--head {
  margin-bottom: 0;
}

@keyframes active-row {
  0% {
    border-color: #fff;
  }

  100% {
    border-color: #333a40;

    /* transform: translateY(0); */
  }
}

/* Responsive Todo Table
  ==================================== */
@media all and (max-width: 750px) {
  #todo-list-flex-table.flexTable {
    display: block;
    width: 100%;
    box-shadow: none;
  }
  #todo-list-flex-table .flexTable-row {
    box-sizing: border-box;
    width: 100%;
    display: flex;
    flex-wrap: wrap;
    box-shadow: 0 0 40px rgba(0, 0, 0, 0.2);
  }

  #todo-list-flex-table.flexTable .flexTable-row .flexTable-cell {
    width: 100% !important;
    display: -webkit-box;
    display: flex;
    -webkit-box-align: center;
    align-items: center;
    padding: 0;
    border-bottom: 1.1px solid #706a6a38;
  }
  #todo-list-flex-table.flexTable
    .flexTable-row
    .flexTable-cell
    .flexTable-cell--heading {
    display: inline-block;
    -webkit-box-flex: 1;
    flex: .6;

    background-color: #fff;
    color: #000;
    font-size: 14px;
    line-height: 1.4;

    font-family: 'Lato', sans-serif;
    font-weight: 700;
  }

  #todo-list-flex-table.flexTable .flexTable-cell.icon-cell {
    border-bottom: 1.1px solid #706a6a38;

    order: -1;
  }
  #todo-list-flex-table.flexTable .flexTable-cell--heading {
    padding: 10px 20px;
    margin: 0;
  }

  #todo-list-flex-table.flexTable
    .flexTable-row
    .flexTable-cell
    .flexTable-cell--content {
    -webkit-box-flex: 2;
    flex: 2;
    padding-left: 0.5rem;
    font-size: 12px;
  }
  .tags-cell {
    padding-left: 0.5rem;
  }
  #todo-list-flex-table.flexTable .icon-content {
    display: flex;
    justify-content: flex-end;
    align-items: center;
  }
  #todo-list-flex-table.flexTable .icon-content > svg {
    margin-right: 1rem;
  }

  #todo-list-flex-table.flexTable .flexTable-row--head {
    display: none;
  }
}
